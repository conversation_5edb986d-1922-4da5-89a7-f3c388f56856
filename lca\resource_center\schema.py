from datetime import date
from typing import Optional

from ninja import Schema
from pydantic import Field

from lca.file.schema import FileOut
from lca.resource_center.models import (
    ImplementationStatus,
    InformationStatus,
    ResourceType,
    StandardScope,
)


class BaseResourceSchema(Schema):
    title: str = Field(..., max_length=50, title="文章标题/标准标题", min_length=1)
    publish_date: date = Field(..., title="发布日期，格式为YYYY-MM-DD")
    publish_organization: str = Field(..., max_length=50, title="发布组织", min_length=1)
    article_link: Optional[str] = Field(None, max_length=255, title="文章链接")
    status: InformationStatus = Field(..., title="内容状态，如草稿或已发布")
    category_id: Optional[str] = Field(None, title="相关行业id")


class BaseResourceInSchema(BaseResourceSchema):
    cover_image_id: Optional[str] = Field(None, title="宣传封面id")
    file_upload_id: Optional[str] = Field(None, title="文件id")


class BaseResourceOutSchema(BaseResourceSchema):
    cover_image: Optional[FileOut] = Field(None, title="宣传封面")
    file_upload: Optional[FileOut] = Field(None, title="文件")


class BaseNewsAndReportPolicySchema(BaseResourceInSchema):
    """信息公告、国内碳资讯、国外碳资讯、研究报告"""

    content: str = Field(None, max_length=30000, title="文章内容")
    summary: str = Field(..., max_length=500, title="文章简介", min_length=1)


class NewsAndAnnouncementInSchema(BaseNewsAndReportPolicySchema):
    """信息公告、国内碳资讯、国外碳资讯、研究报告"""

    content_type: ResourceType = Field(..., title="文章类型")


class NewsAndAnnouncementUpdateSchema(BaseNewsAndReportPolicySchema):
    """更新 信息公告、国内碳资讯、国外碳资讯、研究报告"""

    pass


class StandardSpecificationSchema(BaseResourceInSchema):
    """标准规范"""

    content: Optional[str] = Field(None, max_length=30000, title="文章内容")
    standard_number: str = Field(..., max_length=50, title="标准号", min_length=1)
    standard_scope: StandardScope = Field(..., title="标准范围")
    applicable_area: Optional[list[str]] = Field(..., title="适用区域ID列表")
    implementation_status: ImplementationStatus = Field(
        ...,
        title="实施状态",
    )
    implementation_date: Optional[date] = Field(None, title="实施日期")


class NewsAndAnnouncementOutSchema(BaseResourceOutSchema):
    """返回数据结构"""

    id: int = Field(..., title="数据id")
    content: str = Field(None, max_length=30000, title="文章内容")
    summary: str = Field(..., max_length=500, title="文章简介", min_length=1)
    content_type: ResourceType = Field(..., title="文章类型")


class StandardSpecificationOutSchema(StandardSpecificationSchema):
    """返回数据结构"""

    id: int = Field(..., title="数据id")
    content: Optional[str] = Field(None, max_length=30000, title="文章内容")
    standard_number: str = Field(..., max_length=50, title="标准号", min_length=1)
    standard_scope: StandardScope = Field(..., title="标准范围")
    applicable_area: Optional[list[str]] = Field(..., title="适用区域ID列表")
    implementation_status: ImplementationStatus = Field(
        ...,
        title="实施状态",
    )
    implementation_date: Optional[date] = Field(None, title="实施日期")


class AllResourceCenterOutSchema(BaseResourceOutSchema):
    """返回数据结构"""

    id: int = Field(..., title="数据id")

    content_type: ResourceType | None = Field(..., title="文章类型")
    summary: str | None = Field(None, max_length=500, title="文章简介")

    content: Optional[str] | None = Field(None, max_length=30000, title="文章内容")
    standard_number: str | None = Field(None, max_length=50, title="标准号")
    standard_scope: StandardScope | None = Field(None, title="标准范围")
    applicable_area: Optional[list[str]] | None = Field(None, title="适用区域ID列表")
    implementation_status: ImplementationStatus | None = Field(
        None,
        title="实施状态",
    )
    implementation_date: Optional[date] | None = Field(None, title="实施日期")
