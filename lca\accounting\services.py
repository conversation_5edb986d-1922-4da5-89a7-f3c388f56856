from decimal import Decimal

from django.db import transaction
from ninja.errors import HttpError

from lca.accounting.models import (
    ALLOCATION_METHODS,
    BOUNDARIES,
    LIFE_CYCLE_STAGES,
    OUTPUT_TYPES,
    PRODUCT,
    SOURCE_TYPES,
    Allocation,
    CustomFactor,
    EmissionSource,
    Geography,
    Input,
    LifeCycle,
    Model,
    Output,
    Process,
    Transport,
    Unit,
)
from lca.accounting.schema import (
    AllocationInSchema,
    Co2eEachStageSchema,
    CustomFactorInSchema,
    FactorSchema,
    InputInSchema,
    LcaOutputSchema,
    ModelInSchema,
    OutputInSchema,
    ProcessInSchema,
)
from lca.file.models import File
from lca.users.models import User


class ModelService:
    @staticmethod
    def delete_model(model: Model):
        model.delete()

    @staticmethod
    def create_model(data: ModelInSchema, user: User):
        odata = data
        data = data.model_dump(
            include=[
                "name",
                "functional_unit",
                "category_id",
                "company_name",
                "description",
                "boundary",
                "year",
                "geography_id",
                "rule",
                "amount",
                "unit_id",
                "image_id",
                "specs",
            ]
        )
        # 判断产品名字
        product = None
        for value, label in PRODUCT.choices:
            if data["name"] == label:
                product = value
                break

        data["creator_id"] = user.id
        data["product"] = product
        with transaction.atomic():
            model = Model.objects.create(**data)
            File.objects.filter(id__in=odata.bom_ids).update(bom_id=model.id)
            File.objects.filter(id__in=odata.production_report_ids).update(production_report_id=model.id)
            File.objects.filter(id__in=odata.purchase_contract_ids).update(purchase_contract_id=model.id)
            File.objects.filter(id__in=odata.water_bill_ids).update(water_bill_id=model.id)
            File.objects.filter(id__in=odata.energy_report_ids).update(energy_report_id=model.id)
            File.objects.filter(id__in=odata.energy_tax_ids).update(energy_tax_id=model.id)
            File.objects.filter(id__in=odata.energy_other_ids).update(energy_other_id=model.id)
            File.objects.filter(id__in=odata.environment_impact_report_ids).update(
                environment_impact_report_id=model.id
            )
            File.objects.filter(id__in=odata.environment_check_report_ids).update(environment_check_report_id=model.id)
            File.objects.filter(id__in=odata.outlet_flow_report_ids).update(outlet_flow_report_id=model.id)
            File.objects.filter(id__in=odata.waste_ledger_ids).update(waste_ledger_id=model.id)
            File.objects.filter(id__in=odata.craft_process_ids).update(craft_process_id=model.id)

            # 根据边界创建生命周期
            life_cycles = [LifeCycle(model=model, stage=LIFE_CYCLE_STAGES.PRODUCTION.value)]
            if data["boundary"] == BOUNDARIES.CRADLE_GRAVE.value:
                life_cycles.append(LifeCycle(model=model, stage=LIFE_CYCLE_STAGES.DISTRIBUTION.value))
                life_cycles.append(LifeCycle(model=model, stage=LIFE_CYCLE_STAGES.USE.value))
                life_cycles.append(LifeCycle(model=model, stage=LIFE_CYCLE_STAGES.DISPOSAL.value))

            LifeCycle.objects.bulk_create(life_cycles)

            # 为每个阶段创建默认过程
            for life_cycle in life_cycles:
                process = Process.objects.create(name="单元过程")
                life_cycle.process = process
                life_cycle.save()

        return model

    @staticmethod
    def update_model(model, data: ModelInSchema):
        odata = data
        data = data.model_dump(
            include=[
                "name",
                "functional_unit",
                "category_id",
                "company_name",
                "description",
                "year",
                "geography_id",
                "rule",
                "amount",
                "unit_id",
                "image_id",
            ]
        )
        with transaction.atomic():
            for key, value in data.items():
                setattr(model, key, value)
            File.objects.filter(id__in=odata.bom_ids).update(bom_id=model.id)
            File.objects.filter(id__in=odata.production_report_ids).update(production_report_id=model.id)
            File.objects.filter(id__in=odata.purchase_contract_ids).update(purchase_contract_id=model.id)
            File.objects.filter(id__in=odata.water_bill_ids).update(water_bill_id=model.id)
            File.objects.filter(id__in=odata.energy_report_ids).update(energy_report_id=model.id)
            File.objects.filter(id__in=odata.energy_tax_ids).update(energy_tax_id=model.id)
            File.objects.filter(id__in=odata.energy_other_ids).update(energy_other_id=model.id)
            File.objects.filter(id__in=odata.environment_impact_report_ids).update(
                environment_impact_report_id=model.id
            )
            File.objects.filter(id__in=odata.environment_check_report_ids).update(environment_check_report_id=model.id)
            File.objects.filter(id__in=odata.outlet_flow_report_ids).update(outlet_flow_report_id=model.id)
            File.objects.filter(id__in=odata.waste_ledger_ids).update(waste_ledger_id=model.id)
            File.objects.filter(id__in=odata.craft_process_ids).update(craft_process_id=model.id)
            model.save()

    @staticmethod
    def copy_model(model: Model) -> Model:
        pass

    @staticmethod
    def calc_model_co2e(model: Model):
        """计算模型的CO2e"""
        model.co2e = 0
        for life_cycle in model.life_cycles.all():
            model.co2e += life_cycle.co2e
        model.save()


class LifeCycleService:
    @staticmethod
    def update_process(process: Process, data: ProcessInSchema):
        for key, value in data.model_dump().items():
            setattr(process, key, value)
        process.save()

    @staticmethod
    def create_input(process: Process, data: InputInSchema):
        input = Input(parent_process=process)
        LifeCycleService.update_input(input, data)
        return input

    @staticmethod
    def update_input(input: Input, data: InputInSchema):
        """更新单元过程得输入"""
        emission_source = None
        custom_factor = None
        if data.source_type == SOURCE_TYPES.EMISSION_SOURCE.value:
            emission_source = EmissionSource.objects.get(pk=data.emission_source_id)
        if data.source_type == SOURCE_TYPES.CUSTOM_FACTOR.value:
            # 更新自定义因子
            if data.custom_factor is None:
                raise HttpError(422, "请选择自定义因子")
            custom_factor = LifeCycleService.get_update_custom_factor(input.custom_factor, data.custom_factor)
        if data.source_type == SOURCE_TYPES.PROCESS.value:
            # 如果没有单元过程，创建一个
            if input.process is None:
                process = Process.objects.create(name="单元过程")
                input.process = process

        input.type = data.type
        input.name = data.name
        input.amount = data.amount
        input.unit = Unit.objects.get(pk=data.unit)
        input.source_type = data.source_type
        input.emission_source = emission_source
        input.custom_factor = custom_factor
        input.save()
        input.transports.all().delete()

        for transport in data.transports:
            Transport.objects.create(input=input, **transport.__dict__)
        LifeCycleService.calc_input_co2e(input)

    @staticmethod
    def delete_input(input: Input):
        input.delete()

    @staticmethod
    def create_output(process: Process, data: OutputInSchema):
        output = Output(process=process)
        LifeCycleService.update_output(output, data)
        return output

    @staticmethod
    def update_output(output: Output, data: OutputInSchema):
        """更新输出"""

        for key, value in data.model_dump(exclude=["allocation", "emission_source_id", "custom_factor"]).items():
            setattr(output, key, value)
        # 如果是主产品，同时配置过程的分配方式
        if data.type == OUTPUT_TYPES.MAIN_PRODUCT.value:
            if data.allocation is None or data.allocation.method is None:
                raise HttpError(422, "请选择分配方式")
            output.process.allocation_method = data.allocation.method
            output.process.save()

        # 回收处理
        emission_source = None
        custom_factor = None
        if data.emission_source_id is not None:
            custom_factor = None
            emission_source = EmissionSource.objects.get(id=data.emission_source_id)
        if data.custom_factor is not None:
            emission_source = None
            custom_factor = LifeCycleService.get_update_custom_factor(output.custom_factor, data.custom_factor)

        allocation = None
        if data.allocation is not None:
            allocation = LifeCycleService.get_update_allocation(output.allocation, data.allocation)
        output.allocation = allocation
        output.custom_factor = custom_factor
        output.emission_source = emission_source
        output.save()

    @staticmethod
    def delete_output(output: Output):
        output.delete()

    @staticmethod
    def get_update_custom_factor(custom_factor: CustomFactor, data: CustomFactorInSchema) -> CustomFactor:
        """更新自定义因子"""
        if custom_factor is None:
            custom_factor = CustomFactor()
        for key, value in data.model_dump(exclude=["unit"]).items():
            setattr(custom_factor, key, value)
        custom_factor.unit = Unit.objects.get(id=data.unit)
        custom_factor.save()
        return custom_factor

    @staticmethod
    def get_update_allocation(allocation: Allocation, data: AllocationInSchema):
        """更新分配"""
        if allocation is None:
            allocation = Allocation()

        for key, value in data.model_dump(exclude=["emission_source", "custom_factor", "unit"]).items():
            setattr(allocation, key, value)

        custom_factor = None
        if data.custom_factor is not None:
            custom_factor = LifeCycleService.get_update_custom_factor(allocation.custom_factor, data.custom_factor)
        emission_source = None
        if data.emission_source_id is not None:
            emission_source = EmissionSource.objects.get(id=data.emission_source_id)
        if data.unit:
            allocation.unit = Unit.objects.get(id=data.unit)
        allocation.emission_source = emission_source
        allocation.custom_factor = custom_factor
        allocation.save()
        return allocation

    @staticmethod
    def calc_process_co2e(process: Process):
        """
        计算过程CO2e
        """
        co2e = 0
        for input in process.inputs.all():
            co2e += input.co2e
        for output in process.outputs.all():
            co2e += LifeCycleService.calc_output_co2e(output)

        # 计算主产品的分配
        main_product = process.outputs.filter(type=OUTPUT_TYPES.MAIN_PRODUCT.value).first()
        if main_product is not None:
            allocation = main_product.allocation
            if allocation is not None:
                percent = allocation.percent
                if percent is not None:
                    co2e *= percent / 100

        process.co2e = co2e
        process.save()

    @staticmethod
    def calc_input_co2e(input: Input):
        """
        计算输入CO2e
        """
        factor = None
        if input.source_type == SOURCE_TYPES.PROCESS.value:
            """过程"""
            # 搜索主产品
            process = input.process
            main_product = process.outputs.filter(type=OUTPUT_TYPES.MAIN_PRODUCT.value).first()
            if main_product is not None:
                factor = FactorSchema(
                    co2e=process.co2e,
                    amount=main_product.amount,
                    unit=main_product.unit,
                )
        if input.source_type == SOURCE_TYPES.EMISSION_SOURCE.value:
            factor = input.emission_source
        if input.source_type == SOURCE_TYPES.CUSTOM_FACTOR.value:
            factor = input.custom_factor
        if factor is not None:
            co2e = LifeCycleService.calc_co2e_by_factor(factor, input.amount, input.unit)
            # 运输
            for transport in input.transports.all():
                unit = Unit.objects.get(pk="t*km")
                amount = (
                    input.amount * input.unit.conversion_factor / 1000 * transport.gross_weight * transport.distance
                )  # 转换为t*km
                co2e += LifeCycleService.calc_co2e_by_factor(transport.emission_source, amount, unit)
            input.co2e = co2e
            input.save()

    @staticmethod
    def calc_output_co2e(output: Output):
        """计算输出的CO2e"""
        co2e = 0
        if output.type == OUTPUT_TYPES.ENVIRONMENTAL_EMISSION:
            if output.flow is not None:
                if output.flow.gwp is not None:
                    co2e = output.flow.gwp * output.amount * output.unit.conversion_factor

        if output.type == OUTPUT_TYPES.WASTE_FLOW.value:
            factor = None
            if output.emission_source is not None:
                factor = output.emission_source
            if output.custom_factor is not None:
                factor = output.custom_factor
            if factor is not None:
                co2e = LifeCycleService.calc_co2e_by_factor(factor, output.amount, output.unit)

        if output.type == OUTPUT_TYPES.BY_PRODUCT.value:
            """替代"""
            if output.process.allocation_method == ALLOCATION_METHODS.SYSTEM_EXPANSION.value:
                """系统扩展法，抵扣"""
                factor = None
                if output.emission_source is not None:
                    factor = output.emission_source
                if output.custom_factor is not None:
                    factor = output.custom_factor
                if factor is not None:
                    co2e = -1 * LifeCycleService.calc_co2e_by_factor(factor, output.amount, output.unit)

        return co2e

    @staticmethod
    def calc_co2e_by_factor(factor: FactorSchema, amount: Decimal, unit: Unit):
        """根据因子计算CO2e"""
        return factor.co2e / (factor.unit.conversion_factor * factor.amount) * amount * unit.conversion_factor

    @staticmethod
    def calc_process_production_material_co2e(process: Process):
        """计算过程的原材料与能源获取阶段的CO2e"""
        co2e_material = 0
        co2e_production = 0
        for input in process.inputs.all():
            if input.source_type == SOURCE_TYPES.PROCESS.value:
                # 如果是过程，递归计算
                m, p = LifeCycleService.calc_process_production_material_co2e(input.process)
                co2e_material += m
                co2e_production += p
            else:
                co2e_material += input.co2e
        co2e_production = process.co2e - co2e_material
        return co2e_material, co2e_production

    @staticmethod
    def get_lca(model: Model):
        """获取生命周期结果"""
        boundary_name = ""
        for value, label in BOUNDARIES.choices:
            if model.boundary == value:
                boundary_name = label

        ret = LcaOutputSchema(
            id=model.id,
            name=model.name,
            amount=model.amount,
            unit_name=model.unit.id,
            boundary_name=boundary_name,
            category_name=model.category.name,
            co2e=model.co2e,
        )
        ret.category_name = model.category.name
        ret.has_generated_report = True if model.report_id else False
        co2e_inputs = 0
        co2e_crafts = 0
        for life_cycle in model.life_cycles.filter(stage=LIFE_CYCLE_STAGES.PRODUCTION.value).all():
            co2e_inputs, co2e_crafts = LifeCycleService.calc_process_production_material_co2e(life_cycle.process)
        co2e_each_stage = [
            Co2eEachStageSchema(stage=LIFE_CYCLE_STAGES.RAW_MATERIALS.value, co2e=round(co2e_inputs, 2)),
            Co2eEachStageSchema(stage=LIFE_CYCLE_STAGES.PRODUCTION.value, co2e=round(co2e_crafts, 2)),
        ]

        for x in model.life_cycles.filter(stage=LIFE_CYCLE_STAGES.DISTRIBUTION.value).all():
            co2e_each_stage.append(
                Co2eEachStageSchema(stage=LIFE_CYCLE_STAGES.DISTRIBUTION.value, co2e=round(x.co2e, 2))
            )

        ret.co2e_each_stage = co2e_each_stage

        ret.image = model.image
        ret.report = model.report
        ret.level = {
            "limit": 120,
            "top": 90,
            "percent": 15,
            "unit_id": "kg",
        }
        ret.input_compare = [
            {
                "name": "石灰石",
                "current": {
                    "name": "石灰石 中国",
                    "Geography": {
                        "id": "CN",
                        "name": "中国",
                    },
                    "amount": 2,
                    "unit_id": "kg",
                },
                "advise": {
                    "name": "石灰石 河北",
                    "Geography": {
                        "id": "HB",
                        "name": "河北",
                    },
                    "amount": 1.5,
                    "unit_id": "kg",
                },
            },
            {
                "name": "粉煤灰",
                "current": {
                    "name": "粉煤灰-中国",
                    "Geography": {
                        "id": "CN",
                        "name": "中国",
                    },
                    "amount": 2,
                    "unit_id": "kg",
                },
                "advise": {
                    "name": "粉煤灰-河北",
                    "Geography": {
                        "id": "HB",
                        "name": "河北",
                    },
                    "amount": 1.5,
                    "unit_id": "kg",
                },
            },
        ]

        return ret


class GeographyService:
    """区域服务"""

    @staticmethod
    def check_geography_exists(geography_id: str) -> bool:
        """检查区域"""
        return Geography.objects.filter(id=geography_id).exists()
