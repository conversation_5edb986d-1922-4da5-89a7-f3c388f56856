from datetime import date
from ninja import Query, Router
from infra.filter import filter, next_day
from infra.decorators import certificated_user_required_authority
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_STATUS,
    SUPERVISION_STATUS,
    Certification,
    Supervision,
)
from lca.certification.schema import (
    CarbonFootprintVerificationInSchema,
    CarbonFootprintVerificationSchema,
    CertificateIssuanceInSchema,
    CertificateIssuanceSchema,
    CertificationApplyRejectInSchema,
    CertificationAuthorityIndexSchema,
    CertificationNextInSchema,
    CertificationPlanInSchema,
    CertificationPlanSchema,
    CertificationRowSchema,
    CertificationSchema,
    CertificationStepInfoSchema,
    DocucmentReviewInSchema,
    DocumentReviewSchema,
    ReviewDecisionInSchema,
    ReviewDecisionSchema,
    OnSiteInspectionReportInSchema,
    OnSiteInspectionReportSchema,
    SupervisionInSchema,
    SupervisionRowSchema,
    SupervisionSchema,
    VerificationReportIssuanceSchema,
)
from infra.PageNumberPagination import PageNumberPagination
from ninja.pagination import paginate
from lca.certification.services import CertificationService
from lca.file.service import FileService
from lca.policies import authorize
from ninja.errors import HttpError


router = Router(tags=["碳认证-认证机构"])


@router.get(
    "",
    response=list[CertificationRowSchema],
    summary="认证列表",
    operation_id="authorityGetCertificationList",
)
@paginate(PageNumberPagination)
@certificated_user_required_authority()
def get_certification_list(
    request,
    product_name: str | None = Query(None, title="产品名称"),
    principal: str | None = Query(None, title="委托人"),
    certification_status: CERTIFICATION_STATUS | None = Query(None, title="认证状态"),
    certification_application_status: CERTIFICATION_APPLICATION_STATUS | None = Query(None, title="受理状态"),
    supervision_status: SUPERVISION_STATUS | None = Query(None, title="监督管理状态"),
    date_start: date | None = Query(None, title="申请开始日期"),
    date_end: date | None = Query(None, title="申请结束日期"),
    application_date_start: date | None = Query(None, title="申请完成开始日期"),
    application_date_end: date | None = Query(None, title="申请完成结束日期"),
    certification_date_start: date | None = Query(None, title="认证完成开始日期"),
    certification_date_end: date | None = Query(None, title="认证完成结束日期"),
):
    return filter(
        Certification.objects.order_by("-create_time"),
        authority=request.user.authority,
        product_name__icontains=product_name,
        principal__icontains=principal,
        certification_status=certification_status,
        application_status=certification_application_status,
        supervision_status=supervision_status,
        create_time__gte=date_start,
        create_time__lt=next_day(date_end),
        certification_finish_time__gte=certification_date_start,
        certification_finish_time__lt=next_day(certification_date_end),
        application_finish_time__gte=application_date_start,
        application_finish_time__lt=next_day(application_date_end),
    ).prefetch_related("category", "authority")


@router.get(
    "index",
    response=CertificationAuthorityIndexSchema,
    summary="认证首页",
    operation_id="authorityGetCertificationIndex",
)
@certificated_user_required_authority()
def get_certification_index(request):
    return CertificationService.get_authority_index(request.user.authority)


@router.get(
    "{id}/step-info",
    response=CertificationStepInfoSchema,
    summary="获取认证步骤信息",
    operation_id="authorityGetCertificationStepInfo",
)
@certificated_user_required_authority()
def get_certification_step_info(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "view", item)
    return item


# 批量下载附件文件
@router.post("{id}/download-attachments", summary="批量下载附件文件")
@certificated_user_required_authority()
def download_attachments(request, id: int, file_ids: list[str]):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "view", item)
    return FileService.batch_download(f"认证-{item.product_name}-附件.zip", file_ids)


@router.post(
    "{id}/accept",
    response=CertificationSchema,
    summary="接受受理",
    operation_id="authorityAccept",
)
@certificated_user_required_authority()
def accept(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.accept(item)


@router.post(
    "{id}/reject",
    response=CertificationSchema,
    summary="拒绝受理",
    operation_id="authorityReject",
)
@certificated_user_required_authority()
def reject(request, id: int, data: CertificationApplyRejectInSchema):
    item = Certification.objects.get(pk=id)
    return CertificationService.reject(item, data)


@router.post(
    "{id}/start",
    response=CertificationSchema,
    summary="开始认证",
    operation_id="authorityStart",
)
@certificated_user_required_authority()
def start(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.start(item)


# 制定认证检查方案
@router.post(
    "{id}/plan",
    response=CertificationPlanSchema,
    summary="制定认证检查方案",
    operation_id="authorityCreatePlan",
)
@certificated_user_required_authority()
def create_plan(request, id: int, data: CertificationPlanInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.update_plan(item, data)


# 生成认证检查方案文件
@router.post(
    "{id}/plan/file",
    response=CertificationPlanSchema,
    summary="生成认证检查方案文件",
    operation_id="authorityCreatePlanFile",
)
@certificated_user_required_authority()
def create_plan_file(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.create_plan_file(item)


# 文件评审
@router.post(
    "{id}/document-review",
    response=DocumentReviewSchema,
    summary="文件评审",
    operation_id="authorityDocumentReview",
)
@certificated_user_required_authority()
def document_review(request, id: int, data: DocucmentReviewInSchema):
    # 如果生命周期模型不通过，必须有模型文件；如果认证材料不通过，必须有认证材料文件
    if data.model_accepted is False and data.model_file_id is None:
        raise HttpError(422, "生命周期模型不通过，必须有模型文件")
    if data.material_accepted is False and data.material_file_id is None:
        raise HttpError(422, "认证材料不通过，必须有认证材料文件")

    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.create_document_review(item, data)


# 现场检查
@router.post(
    "{id}/onsite-inspection",
    response=OnSiteInspectionReportSchema,
    summary="现场检查",
    operation_id="authorityOnsiteInspection",
)
@certificated_user_required_authority()
def onsite_inspection(request, id: int, data: OnSiteInspectionReportInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    if (
        data.capability_accepted is False
        or data.name_file_accepted is False
        or data.design_file_accepted is False
        or data.design_product_accepted is False
    ):
        if data.nonconformities is None:
            raise HttpError(422, "企业保证能力检查不通过，必须有整改建议")

    return CertificationService.create_onsite_inspection(item, data)


@router.post(
    "{id}/onsite-inspection/file",
    response=OnSiteInspectionReportSchema,
    summary="生成现场检查报告",
    operation_id="authorityCreateOnSiteInspectionFile",
)
@certificated_user_required_authority()
def create_onsite_inspection_file(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.create_onsite_inspection_file(item)


# 碳足迹核查
@router.post(
    "{id}/carbon-footprint-verification",
    response=CarbonFootprintVerificationSchema,
    summary="碳足迹核查",
    operation_id="authorityCarbonFootprintVerification",
)
@certificated_user_required_authority()
def carbon_footprint_verification(request, id: int, data: CarbonFootprintVerificationInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    if data.result is False and data.result_file_id is None:
        raise HttpError(422, "核查结果不通过，必须有审批文件")
    return CertificationService.create_carbon_footprint_verification(item, data)


# 生成碳足迹核查报告 VerificationReportIssuanceInSchema
@router.post(
    "{id}/verification-report-issuance",
    response=VerificationReportIssuanceSchema,
    summary="生成碳足迹核查报告",
    operation_id="authorityCreateVerificationReportIssuance",
)
@certificated_user_required_authority()
def create_verification_report_issuance(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.create_verification_report_issuance(item)


# 复合与认证
@router.post(
    "{id}/review-decision",
    response=ReviewDecisionSchema,
    summary="复合与认证",
    operation_id="authorityReviewDecision",
)
@certificated_user_required_authority()
def review_decision(request, id: int, data: ReviewDecisionInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    if data.result is False and data.result_file_id is None:
        raise HttpError(422, "认证决定不通过，必须有审批文件")
    return CertificationService.create_review_decision(item, data)


# 发放证书及标识
@router.post(
    "{id}/certificate-issuance",
    response=CertificateIssuanceSchema,
    summary="发放证书及标识",
    operation_id="authorityCertificateIssuance",
)
@certificated_user_required_authority()
def certificate_issuance(request, id: int, data: CertificateIssuanceInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.create_certificate_issuance(item, data)


# 下一步
@router.post(
    "{id}/next",
    response=CertificationSchema,
    summary="下一步/结束/完成认证",
    operation_id="authorityNext",
)
@certificated_user_required_authority()
def next(request, id: int, data: CertificationNextInSchema):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.next_step_or_finish(item, data.current_step)


# 监督列表 搜索 产品名称 申请企业名称
@router.get(
    "/supervision",
    response=list[SupervisionRowSchema],
    summary="监督管理列表",
    operation_id="authorityGetSupervisionList",
)
@paginate(PageNumberPagination)
@certificated_user_required_authority()
def get_supervision_list(
    request,
    product_name: str | None = Query(None, title="产品名称"),
    manufacturer_name: str | None = Query(None, title="申请企业名称"),
    principal: str | None = Query(None, title="委托人"),
    status: SUPERVISION_STATUS | None = Query(None, title="监督管理状态"),
):
    return filter(
        Supervision.objects.order_by("-start_time"),
        certification__product_name__icontains=product_name,
        manufacturer__name__icontains=manufacturer_name,
        certification__principal__icontains=principal,
        status=status,
        authority=request.user.authority,
    )


# 开始监督
@router.post(
    "/supervision/{id}/start",
    response=SupervisionSchema,
    summary="开始监督",
    operation_id="authorityStartSupervision",
)
@certificated_user_required_authority()
def start_supervision(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "certificate", item)
    return CertificationService.start_supervision(item)


# 监督详情
@router.get(
    "/supervision/{id}",
    response=SupervisionSchema,
    summary="监督管理详情",
    operation_id="authorityGetSupervisionDetail",
)
@certificated_user_required_authority()
def get_supervision_detail(request, id: int):
    item = Supervision.objects.get(pk=id)
    authorize(request.user, "view", item.certification)
    return item


# 监督检查
@router.post(
    "/supervision/{id}/check",
    response=SupervisionSchema,
    summary="监督检查",
    operation_id="authorityCheckSupervision",
)
@certificated_user_required_authority()
def check_supervision(request, id: int, data: SupervisionInSchema):
    item = Supervision.objects.get(pk=id)
    authorize(request.user, "certificate", item.certification)
    if data.result is False and data.result_file_id is None:
        raise HttpError(422, "监督决定不通过，必须有审批文件")
    CertificationService.check_supervision(item, data)
    return item


@router.post(
    "/supervision/{id}/finish",
    response=SupervisionSchema,
    summary="完成监督检查",
    operation_id="authorityFinishSupervision",
)
@certificated_user_required_authority()
def finish_supervision(request, id: int):
    item = Supervision.objects.get(pk=id)
    authorize(request.user, "certificate", item.certification)
    CertificationService.finish_supervision(item)
    return item


@router.get(
    "{id}",
    response=CertificationSchema,
    summary="认证详情",
    operation_id="authorityGetCertificationDetail",
)
@certificated_user_required_authority()
def get_certification_detail(request, id: int):
    item = Certification.objects.get(pk=id)
    authorize(request.user, "view", item)
    return item
