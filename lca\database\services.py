from datetime import datetime
from lca.accounting.models import EmissionSource
from lca.common.categoryServices import CategoryServices
from django.template.loader import render_to_string


class DatabaseService:
    @staticmethod
    def search(text: str = "", category_id: str | None = None, **kwargs):
        query = EmissionSource.objects.filter(**kwargs)
        if category_id:
            ids = CategoryServices.get_all_id(category_id)
            query = query.filter(category_id__in=ids)
        if text:
            query = query.filter(name__icontains=text)

        return query.all()

    @staticmethod
    def export_process_ilcd(process: EmissionSource):
        start, end = DatabaseService.get_emission_source_start_end(process.dataset.year)
        cats = []
        cat = process.dataset.category
        while cat:
            cats.insert(0, cat)
            cat = cat.parent
        data = dict(
            process=process,
            dataset=process.dataset,
            management=process.management,
            inputs=process.einputs.all(),
            outputs=process.eoutputs.all(),
            start=start.strftime("%Y"),
            end=end.strftime("%Y"),
            start_time=int(start.timestamp()) * 1000,
            end_time=int(end.timestamp()) * 1000,
            management_time=process.management.generate_create_time.isoformat(),
            cats=cats,
            create_time=process.management.generate_create_time.isoformat(),
            last_update=process.management.generate_update_time.isoformat(),
        )
        return render_to_string("process.json", data)

    @staticmethod
    def get_emission_source_start_end(year: str):
        """转换年份的开始和结束时间"""
        times = year.split("-")
        start = datetime(int(times[0]), 1, 1)
        end = datetime(int(times[len(times) - 1]), 12, 31)
        return start, end
