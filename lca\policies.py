from lca.accounting.models import Input, Model, Output, Process
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_STATUS,
    Certification,
)
from lca.users.models import User
from ninja.errors import HttpError


class CertificationPolicy:
    @staticmethod
    def view(user: User, certification: Certification) -> bool:
        return certification.manufacturer.user == user or certification.authority.user == user

    @staticmethod
    def update(user: User, certification: Certification) -> bool:
        if certification.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
            raise HttpError(403, "当前状态不允许该操作")

        return certification.manufacturer.user == user

    @staticmethod
    def withdraw(user: User, certification: Certification) -> bool:
        if certification.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
            raise HttpError(403, "当前状态不允许该操作")

        return certification.manufacturer.user == user

    @staticmethod
    def accept(user: User, certification: Certification) -> bool:
        if certification.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
            raise HttpError(403, "当前状态不允许该操作")

        return certification.authority.user == user

    @staticmethod
    def certificate(user: User, certification: Certification) -> bool:
        """可以做认证相关的"""
        return certification.authority.user == user


def model_user_required(user, obj: Model | Process | Input | Output):
    # 判断process、input、output 的model的用户。注意一个过程的上级可能是一个输入/输出
    if isinstance(obj, Model):
        model = obj
    elif isinstance(obj, Process):
        input = obj.input
        if input is not None:
            obj = input
            return model_user_required(user, obj)
        model = obj.life_cycle.model
    elif isinstance(obj, Input):
        process = obj.parent_process
        obj = process
        return model_user_required(user, obj)
    elif isinstance(obj, Output):
        return model_user_required(user, obj.process)
    else:
        raise HttpError(400, "不支持的类型")

    if model is None:
        raise HttpError(400, "不支持的类型")

    return model.creator == user


class ProcessPolicy:
    @staticmethod
    def view(user: User, process: Process) -> bool:
        return model_user_required(user, process)

    @staticmethod
    def update(user: User, process: Process) -> bool:
        return model_user_required(user, process)


class InputPolicy:
    @staticmethod
    def view(user: User, input: Input) -> bool:
        return model_user_required(user, input)

    @staticmethod
    def update(user: User, input: Input) -> bool:
        return model_user_required(user, input)


class OutputPolicy:
    @staticmethod
    def view(user: User, output: Output) -> bool:
        return model_user_required(user, output)

    @staticmethod
    def update(user: User, output: Output) -> bool:
        return model_user_required(user, output)


class ModelPolicy:
    @staticmethod
    def view(user: User, model: Model) -> bool:
        if model.creator == user:
            return True
        # 如果认证机构有收到该模型的认证申请，可以查看
        if Certification.objects.filter(model=model, authority__user=user).exists():
            return True

    @staticmethod
    def update(user: User, model: Model) -> bool:
        # 如果有正在进行中的、已通过的认证，不能修改
        if model.certifications.filter(
            certification_status__in=[
                CERTIFICATION_STATUS.ONGOING.value,
                CERTIFICATION_STATUS.APPROVED.value,
            ]
        ).exists():
            raise HttpError(403, "当前状态不允许该操作（有正在进行中、已通过的认证）")
        return model.creator == user


def authorize(user: User, action: str, obj: Model):
    # 根据 model 名称，获取对应的 policy 类
    policy_class = globals()[obj.__class__.__name__ + "Policy"]
    result = policy_class.__dict__[action](user, obj)

    if result is True:
        return

    if user.is_superuser:
        return

    raise HttpError(403, "您没有权限执行该操作")
