"""
Django settings for mysite project.

Generated by 'django-admin startproject' using Django 5.1.6.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

import os
import environ
import sys


# 默认值
env = environ.Env(
    DEBUG=(bool, False),
    SCHEDULER_SWITCH=(bool, False),
)
env.read_env()
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env("SECRET_KEY")
MINIO_ENDPOINT = env("OSS_ENDPOINT")
MINIO_BUCKET = env("OSS_BUCKET")
MINIO_ACCESS_KEY = env("OSS_ACCESS_KEY_ID")
MINIO_SECRET_KEY = env("OSS_SECRET_ACCESS_KEY")
MINIO_PREFIX = env("OSS_PREFIX")
MINIO_INTERNAL_PREFIX = env("OSS_INTERNAL_PREFIX")
# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env("DEBUG")
AUTH_USER_MODEL = "lca.User"
ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    "corsheaders",
    "django.contrib.auth",
    "django.contrib.sessions",
    "django.contrib.contenttypes",
    "lca.apps.LcaConfig",
    "ztbory_django_shield.ninja",
]

SHIELD = {
    # SM2加密
    "cryptor": {
        # 私钥
        "private_key": "<PRIVATE_KEY>",
        # 公钥
        "public_key": "<PUBLIC_KEY>",
    },
    # 密码防护
    "password": {
        # # 密码复杂度校验
        # "complex_content_regex": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d!\\\"#$%&'()*+,-./:;<=>?@\\\\\\]\\[^_`{|}~]",
        # "complex_min_length": 8,
        # "complex_max_length": 32,
        # "complex_tips_template": (
        #     "密码应包含{complex_min_length}-{complex_max_length}位字符，"
        #     "要求同时具备英文大写、小写、阿拉伯数字"
        # ),
        # # 密码错误防护
        # "incorrect_ban_with_ip": True,
        # "incorrect_max_times": 6,
        # "incorrect_ban_seconds": 3600,
        # # 触发双因子验证错误次数 0代表每次登录均需要双因子验证
        # "incorrect_trigger_two_factor_times": 0,
        # # 触发双因子验证 管理员用户
        # "incorrect_trigger_tow_factor_managers": [],
        # "incorrect_tips_template": (
        #     "登录失败, "
        #     "失败{incorrect_max_times}次账户将会锁定{incorrect_ban_expire}，"
        #     "目前错误次数：{incorrect_times}."
        # ),
        # "incorrect_baned_tips_template": (
        #     "您的账户因多次密码输入错误已被暂时锁定，{incorrect_ban_duration}后重新登录。"
        # ),
    },
    # 图片滑动验证码
    "slide_captcha": {
        # "gallery": "DefaultGallery", # DefaultGallery-常规图库 LjflGallery-垃圾分类图库
        # "width": 460,
        # "height": 260,
        # "expire_in_seconds": 300,
    },
    # 邮箱密码找回（需要搭配Django的email配置）
    # 需要搭配Django发送邮件配置，详情见 https://docs.djangoproject.com/zh-hans/5.1/ref/settings/#email-backend
    "email": {
        # https 图片, 否则无法显示
        "logo_url": "https://domain.com/logo.png",
        "reset_password_url": "https://domain.com/reset-password/?token={token}",
        # "expire_in_seconds": 7200,
    },
}

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
]

CORS_ALLOW_ALL_ORIGINS = True
ROOT_URLCONF = "config.urls"
WSGI_APPLICATION = "config.wsgi.application"
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://:{env('REDIS_PASSWORD')}@{env('REDIS_SERVICE_HOST')}:{env('REDIS_SERVICE_PORT')}/3",
        "KEY_PREFIX": "lca",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR + "/" + "templates"],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {"verbose": {"format": "%(levelname)s %(asctime)s %(module)s %(process)d %(thread)d %(message)s"}},
    "filters": {
        "require_debug_false": {
            "()": "django.utils.log.RequireDebugFalse",
        }
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
    },
    "root": {"level": "INFO", "handlers": ["console"]},
    "loggers": {
        "django.db.backends": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": False,
        },
    },
}

# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "lca",
        "USER": env("DB_USERNAME"),
        "PASSWORD": env("DB_PASSWORD"),
        "HOST": env("DB_HOST"),
        "PORT": env("DB_PORT"),
    },
}

# Use SQLite for testing
if "test" in sys.argv or "pytest" in sys.modules:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": ":memory:",
        }
    }

# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

STATIC_ROOT = os.path.join(BASE_DIR, "static")
if not os.path.exists(STATIC_ROOT):
    os.mkdir(STATIC_ROOT)

TEMPLATE_ROOT = os.path.join(STATIC_ROOT, "templates")
if not os.path.exists(TEMPLATE_ROOT):
    os.mkdir(TEMPLATE_ROOT)

TTF_ROOT = os.path.join(STATIC_ROOT, "fonts")
if not os.path.exists(TTF_ROOT):
    os.mkdir(TTF_ROOT)

LANGUAGE_CODE = "en-us"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = True

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

DIFY_API_URL = env("DIFY_API_URL")
DIFY_API_KEY = env("DIFY_API_KEY")
DIFY_BOM_API_KEY = env("DIFY_BOM_API_KEY")
DIFY_INPUT_CONTROLL_API_KEY = env("DIFY_INPUT_CONTROLL_API_KEY")
DIFY_CRAFT_CONTROLL_API_KEY = env("DIFY_CRAFT_CONTROLL_API_KEY")
DIFY_DISTRIBUTION_CONTROLL_API_KEY = env("DIFY_DISTRIBUTION_CONTROLL_API_KEY")
DIFY_GOAL_SCOPE_API_KEY = env("DIFY_GOAL_SCOPE_API_KEY")
DIFY_ANALYSIS_REPORT_API_KEY = env("DIFY_ANALYSIS_REPORT_API_KEY")

ALIBABA_CLOUD_ACCESS_KEY_ID = env("ALIBABA_CLOUD_ACCESS_KEY_ID")
ALIBABA_CLOUD_ACCESS_KEY_SECRET = env("ALIBABA_CLOUD_ACCESS_KEY_SECRET")
ALIBABA_CLOUD_SMS_SIGN = env("ALIBABA_CLOUD_SMS_SIGN")
ALIBABA_CLOUD_SMS_CODE_TEMPLATE = env("ALIBABA_CLOUD_SMS_CODE_TEMPLATE")
