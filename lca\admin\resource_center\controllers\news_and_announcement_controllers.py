from typing import List, Optional

from ninja import Query, Router
from ninja.pagination import paginate

from infra.PageNumberPagination import PageNumberPagination
from lca.resource_center.models import InformationStatus, ResourceType
from lca.resource_center.schema import (
    NewsAndAnnouncementInSchema,
    NewsAndAnnouncementOutSchema,
    NewsAndAnnouncementUpdateSchema,
)
from lca.resource_center.services import ResourceCenterService

router = Router(tags=["后台-资料库-公告资讯报告"])


@router.get(
    "",
    response=List[NewsAndAnnouncementOutSchema],
    summary="获取公告资讯报告列表",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="listNewsAndAnnouncement",
)
@paginate(PageNumberPagination)
def list_news_policies(
    request,
    content_type: ResourceType = Query(..., title="内容类型"),
    status: InformationStatus = Query(..., title="内容状态"),
    title: Optional[str] = Query(None, title="文章标题"),
    publish_organization: Optional[str] = Query(None, title="发布组织"),
):
    return ResourceCenterService.list_news_announcement(content_type, status, title, publish_organization)


@router.post(
    "",
    response={201: NewsAndAnnouncementOutSchema},
    summary="创建公告资讯报告",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="createNewsAndAnnouncement",
)
def create_news_and_announcement(request, payload: NewsAndAnnouncementInSchema):
    news_and_announcement = ResourceCenterService.create_news_and_announcement(payload)
    return 201, news_and_announcement


@router.get(
    "/{id_}",
    response=NewsAndAnnouncementOutSchema,
    summary="获取公告资讯报告详情",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="getNewsAndAnnouncement",
)
def get_news_and_announcement(request, id_: int):
    return ResourceCenterService.get_detail(id_)


@router.put(
    "/{id_}",
    response=NewsAndAnnouncementOutSchema,
    summary="更新公告资讯报告",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="updateNewsAndAnnouncement",
)
def update_news_and_announcement(request, id_: int, payload: NewsAndAnnouncementUpdateSchema):
    news_and_announcement = ResourceCenterService.update_news_and_announcement(id_, payload)
    return news_and_announcement


@router.delete(
    "/{id_}",
    response={204: None},
    summary="删除公告资讯报告",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="deleteNewsAndAnnouncement",
)
def delete_news_and_announcement(request, id_: int):
    ResourceCenterService.delete(id_)
    return 204, None


@router.put(
    "/{id_}/publish",
    response={200: None},
    summary="发布公告资讯报告",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="publishNewsAndAnnouncement",
)
def publish_news_and_announcement(request, id_: int):
    ResourceCenterService.publish(id_)


@router.put(
    "/{id_}/unpublish",
    response={200: None},
    summary="下架公告资讯报告",
    description="信息公告、国内碳资讯、国外碳资讯、研究报告",
    operation_id="unpublishNewsAndAnnouncement",
)
def unpublish_news_and_announcement(request, id_: int):
    ResourceCenterService.unpublish(id_)
