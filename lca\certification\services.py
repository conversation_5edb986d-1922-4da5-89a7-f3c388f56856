from datetime import date, datetime, timedelta
from django.utils import timezone
from infra.utils import render_docx
from lca.accounting.models import BOUNDARIES, Model
from lca.certification.models import (
    CERTIFICATION_APPLICATION_STATUS,
    CERTIFICATION_LIFE_CYCLES,
    CERTIFICATION_STATUS,
    CERTIFICATION_STEP_STATUS,
    CERTIFICATION_STEPS,
    SUPERVISION_STATUS,
    Authority,
    CertificateIssuance,
    Certification,
    CertificationCarbonFootprintVerification,
    CertificationDocumentReview,
    CertificationOnsiteInspection,
    CertificationPlan,
    CertificationReviewDecision,
    CertificationVerificationReportIssuance,
    Manufacturer,
    Supervision,
)
from lca.certification.schema import (
    CarbonFootprintVerificationInSchema,
    CertificateIssuanceSchema,
    CertificationApplyRejectInSchema,
    CertificationInSchema,
    CertificationPlanInSchema,
    DocucmentReviewInSchema,
    OnSiteInspectionReportInSchema,
    ReviewDecisionInSchema,
    SupervisionInSchema,
)
from lca.file.models import File
from ninja.errors import HttpError
from django.db import transaction
from django.db.models import Count
from lca.file.service import FileService


class CertificationService:
    @staticmethod
    def create(data: CertificationInSchema, manufacturer: Manufacturer, authority: Authority) -> Certification:
        item = Certification(
            manufacturer=manufacturer,
            authority=authority,
            application_status=CERTIFICATION_APPLICATION_STATUS.WAITING.value,
            # 发起认证申请 装维为已完成
            submission_status=CERTIFICATION_STEP_STATUS.COMPLETED.value,
        )

        CertificationService.update(item, data)
        return item

    @staticmethod
    def update(item: Certification, data: CertificationInSchema) -> Certification:
        odata = data
        data = data.model_dump(
            include=[
                "model_id",
                "principal",
                "principal_address",
                "category_id",
                "production_process",
                "manufacturer_name",
                "manufacturer_address",
                "producer_name",
                "producer_address",
                "contact_name",
                "contact_phone",
                "summary",
                "product_name",
                "product_description",
                "product_standard",
                "product_performance",
                "product_function",
                "image_id",
                "business_license_id",
                "agency_relationship_id",
                "letter_of_authorization_id",
                "intellectual_property_id",
                "organizational_chart_id",
                "bom_id",
            ]
        )

        with transaction.atomic():
            if item.id is not None:
                item = Certification.objects.select_for_update().get(pk=item.id)

            # 只有待受理的才能更新
            if item.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
                raise HttpError(422, "当前状态不允许该操作")

            for key, value in data.items():
                setattr(item, key, value)
            item.save()

            if odata.process_flow_ids is not None:
                File.objects.filter(id__in=odata.process_flow_ids).update(process_flow_id=item.id)
            if odata.equipment_ids is not None:
                File.objects.filter(id__in=odata.equipment_ids).update(equipment_id=item.id)
            if odata.label_ids is not None:
                File.objects.filter(id__in=odata.label_ids).update(label_id=item.id)
            if odata.plan_ids is not None:
                File.objects.filter(id__in=odata.plan_ids).update(carbon_reduction_plan_id=item.id)
            if odata.capabilitie_ids is not None:
                File.objects.filter(id__in=odata.capabilitie_ids).update(capability_id=item.id)
            if odata.other_ids is not None:
                File.objects.filter(id__in=odata.other_ids).update(other_id=item.id)
        return item

    @staticmethod
    def withdraw(item: Certification) -> Certification:
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            # 只有待受理的才能撤回
            if item.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
                raise HttpError(422, "当前状态不允许该操作")

            data = dict(
                application_status=CERTIFICATION_APPLICATION_STATUS.WITHDRAWN.value,
                # application_finish_time=datetime.now(),
                submission_status=CERTIFICATION_STEP_STATUS.WITHDROWN.value,
            )
            for key, value in data.items():
                setattr(item, key, value)
            item.save()
        return item

    @staticmethod
    def accept(item: Certification) -> Certification:
        """受理"""
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
                raise HttpError(422, "当前状态不允许该操作")
            data = dict(
                application_status=CERTIFICATION_APPLICATION_STATUS.ACCEPTED.value,
                certification_status=CERTIFICATION_STATUS.WAITING.value,
                application_finish_time=datetime.now(),
                acceptance_review_status=CERTIFICATION_STEP_STATUS.COMPLETED.value,
            )
            for key, value in data.items():
                setattr(item, key, value)
            item.save()
        return item

    @staticmethod
    def reject(item: Certification, data: CertificationApplyRejectInSchema) -> Certification:
        """拒绝受理"""
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.application_status != CERTIFICATION_APPLICATION_STATUS.WAITING.value:
                raise HttpError(422, "当前状态不允许该操作")
            data = dict(
                application_status=CERTIFICATION_APPLICATION_STATUS.REJECTED.value,
                application_finish_time=datetime.now(),
                reason=data.reason,
                acceptance_review_status=CERTIFICATION_STEP_STATUS.REJECTED.value,
            )
            for key, value in data.items():
                setattr(item, key, value)
            item.save()
        return item

    @staticmethod
    def start(item: Certification) -> Certification:
        """开始认证"""
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.application_status != CERTIFICATION_APPLICATION_STATUS.ACCEPTED.value:
                raise HttpError(422, "当前状态不允许该操作")
            if item.certification_status != CERTIFICATION_STATUS.WAITING.value:
                raise HttpError(422, "当前状态不允许该操作")

            data = dict(
                certification_status=CERTIFICATION_STATUS.ONGOING.value,
                certification_start_time=timezone.now(),
                plan_status=CERTIFICATION_STEP_STATUS.ONGOING.value,
            )
            for key, value in data.items():
                setattr(item, key, value)
            item.save()

        return item

    @staticmethod
    def finish(item: Certification) -> Certification:
        """完成"""
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.certification_status != CERTIFICATION_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            data = dict(
                certification_status=CERTIFICATION_STATUS.APPROVED.value,
                certification_finish_time=datetime.now(),
            )
            for key, value in data.items():
                setattr(item, key, value)
            item.save()
        return item

    @staticmethod
    def search(
        principal: str = None,
        authority: Authority = None,
        product_name: str = None,
        manufacturer_name: str = None,
        authority_name: str = None,
        certification_status: str = None,
        certification_application_status: str = None,
        supervision_status: str = None,
        date_start: date = None,
        date_end: date = None,
        certification_date_start: date = None,
        certification_date_end: date = None,
        application_date_start: date = None,
        application_date_end: date = None,
    ):
        query = Certification.objects.order_by("-create_time").all()
        if authority is not None:
            query = query.filter(authority=authority)
        if principal is not None:
            query = query.filter(principal__icontains=principal)
        if product_name is not None:
            query = query.filter(product_name__icontains=product_name)
        if manufacturer_name is not None:
            query = query.filter(manufacturer_name__icontains=manufacturer_name)
        if authority_name is not None:
            query = query.filter(
                authority_id__in=Authority.objects.filter(name__icontains=authority_name).values_list("id", flat=True)
            )
        if certification_status is not None:
            query = query.filter(certification_status=certification_status)
        if certification_application_status:
            query = query.filter(application_status=certification_application_status)
        if supervision_status:
            query = query.filter(supervision_status=supervision_status)
        if date_start is not None:
            query = query.filter(create_time__gte=date_start)
        if date_end is not None:
            tomorrow = date_end + timedelta(days=1)
            query = query.filter(create_time__lt=tomorrow)
        if certification_date_start is not None:
            query = query.filter(certification_finish_time__gte=certification_date_start)
        if certification_date_end is not None:
            tomorrow = certification_date_end + timedelta(days=1)
            query = query.filter(certification_finish_time__lt=tomorrow)
        if application_date_start is not None:
            query = query.filter(application_finish_time__gte=application_date_start)
        if application_date_end is not None:
            tomorrow = application_date_end + timedelta(days=1)
            query = query.filter(application_finish_time__lt=tomorrow)
        return query

    @staticmethod
    def get_num():
        return Certification.objects.filter(certification_status=CERTIFICATION_STATUS.APPROVED.value).aggregate(
            certification_num=Count("id"),
            product_num=Count("id"),
            manufacturer_num=Count("manufacturer_id", distinct=True),
        )

    @staticmethod
    def copy_file_from_model(model: Model):
        """从模型复制文件"""
        bom = None
        process_flows = []
        model_flows = model.craft_processes.all()
        model_boms = model.boms.all()
        if len(model_boms) > 0:
            bom = FileService.copy(model_boms[0])

        if len(model_flows) > 0:
            for file in model_flows:
                file = FileService.copy(file)
                process_flows.append(file)
        return dict(bom=bom, process_flows=process_flows)

    @staticmethod
    def update_plan(item: Certification, data: CertificationPlanInSchema):
        with transaction.atomic():
            CertificationPlan.objects.select_for_update().filter(certification=item).first()
            if item.plan_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            plan, _ = CertificationPlan.objects.update_or_create(
                certification=item,
                defaults={
                    "file_id": None,  # 修改的时候删除文件
                    **data.model_dump(),
                },
            )
        return plan

    @staticmethod
    def create_plan_file(item: Certification):
        with transaction.atomic():
            plan = CertificationPlan.objects.select_for_update().get(certification=item)
            if item.plan_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            file = render_docx("认证检查方案.docx", "认证检查方案.docx", plan.__dict__)
            plan.file = file
            plan.save()
        return plan

    @staticmethod
    def create_document_review(item: Certification, data: DocucmentReviewInSchema):
        with transaction.atomic():
            document_review = CertificationDocumentReview.objects.select_for_update().filter(certification=item).first()
            if item.document_review_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            document_review, _ = CertificationDocumentReview.objects.update_or_create(
                certification=item,
                defaults={
                    "model_accepted": data.model_accepted,
                    "material_accepted": data.material_accepted,
                    "model_file_id": data.model_file_id,
                    "material_file_id": data.material_file_id,
                },
            )
            return document_review

    @staticmethod
    def create_onsite_inspection(item: Certification, data: OnSiteInspectionReportInSchema):
        with transaction.atomic():
            onsite_inspection = (
                CertificationOnsiteInspection.objects.select_for_update().filter(certification=item).first()
            )
            if item.onsite_inspection_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            onsite_inspection, _ = CertificationOnsiteInspection.objects.update_or_create(
                certification=item, defaults={"file_id": None, **data.model_dump()}
            )
            return onsite_inspection

    @staticmethod
    def create_onsite_inspection_file(item: Certification):
        with transaction.atomic():
            onsite_inspection = CertificationOnsiteInspection.objects.select_for_update().get(certification=item)
            if item.onsite_inspection_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            file = render_docx("现场检查报告.docx", "现场检查报告.docx", onsite_inspection.__dict__)
            onsite_inspection.file = file
            onsite_inspection.save()
        return onsite_inspection

    @staticmethod
    def create_carbon_footprint_verification(item: Certification, data: CarbonFootprintVerificationInSchema):
        """碳足迹核查"""
        with transaction.atomic():
            carbon_footprint_verification = (
                CertificationCarbonFootprintVerification.objects.select_for_update().filter(certification=item).first()
            )
            if item.carbon_footprint_verification_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            carbon_footprint_verification, _ = CertificationCarbonFootprintVerification.objects.update_or_create(
                certification=item, defaults={**data.model_dump()}
            )
            return carbon_footprint_verification

    @staticmethod
    # 出具碳足迹核查报告
    def create_verification_report_issuance(item: Certification):
        with transaction.atomic():
            verification_report_issuance = (
                CertificationVerificationReportIssuance.objects.select_for_update().filter(certification=item).first()
            )
            if item.verification_report_issuance_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            data = dict(
                principal=item.principal,
                manufacturer_name=item.manufacturer_name,
                product_description=item.product_description,
                producer_name=item.producer_name,
                product_function=item.product_function,
                product_performance=item.product_performance,
                production_process=item.production_process,
                quantification_method=item.carbon_footprint_verification.quantification_method,
                data_acquisition_principle=item.carbon_footprint_verification.data_acquisition_principle,
                data_quality_requirement=item.carbon_footprint_verification.data_quality_requirement,
                basis=item.onsite_inspection.basis,
                purpose=item.plan.purpose,
                specs=item.carbon_footprint_verification.specs,
                client_name=item.carbon_footprint_verification.client_name,
                address=item.carbon_footprint_verification.address,
                credit_code=item.carbon_footprint_verification.credit_code,
                product_name=item.carbon_footprint_verification.product_name,
                functional_unit=item.carbon_footprint_verification.functional_unit,
                authority_name=item.authority.name,
                verification_date=item.carbon_footprint_verification.verification_date,
                date=datetime.now().strftime("%Y-%m-%d"),
                legal_representative=item.carbon_footprint_verification.legal_representative,
                summary=item.summary,
                contact_name=item.contact_name,
                contact_phone=item.contact_phone,
                product_standard=item.product_standard,
                inspectors=item.plan.inspectors,
                accompanying_inspectors=item.plan.accompanying_inspectors,
                progress_schedule=item.plan.progress_schedule,
                boundary=item.carbon_footprint_verification.boundary,
                data_time_range=item.plan.data_time_range,
                data_quality_evaluation=item.carbon_footprint_verification.data_quality_evaluation,
                followup_inspections=item.carbon_footprint_verification.followup_inspections,
                verification_conclusion=item.carbon_footprint_verification.verification_conclusion,
                verification_recommendation=item.carbon_footprint_verification.verification_recommendation,
                uncertainty_analysis=item.carbon_footprint_verification.uncertainty_analysis,
                product_quantifications=item.carbon_footprint_verification.product_quantifications,
                initial_inspections=item.carbon_footprint_verification.initial_inspections,
                reviewers=item.plan.reviewers,
                unit=item.model.unit_id,
            )

            for row in data["initial_inspections"]:
                row["life_cycle"] = CERTIFICATION_LIFE_CYCLES(row["life_cycle"]).label
            for row in data["followup_inspections"]:
                row["life_cycle"] = CERTIFICATION_LIFE_CYCLES(row["life_cycle"]).label
            for row in data["product_quantifications"]:
                row["life_cycle"] = CERTIFICATION_LIFE_CYCLES(row["life_cycle"]).label

            # 将 boundary 替换为中文
            for value, label in BOUNDARIES.choices:
                if data["boundary"] == value:
                    data["boundary"] = label
                    break
            file = render_docx("产品碳足迹核查报告.docx", "产品碳足迹核查报告.docx", data)

            verification_report_issuance, _ = CertificationVerificationReportIssuance.objects.update_or_create(
                certification=item,
                defaults={
                    "report_file": file,
                },
            )
            return verification_report_issuance

    @staticmethod
    def create_review_decision(item: Certification, data: ReviewDecisionInSchema):
        with transaction.atomic():
            review_decision = CertificationReviewDecision.objects.select_for_update().filter(certification=item).first()
            if item.review_decision_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            review_decision, _ = CertificationReviewDecision.objects.update_or_create(
                certification=item,
                defaults={
                    "result": data.result,
                    "result_file_id": data.result_file_id,
                },
            )
            return review_decision

    @staticmethod
    def create_certificate_issuance(item: Certification, data: CertificateIssuanceSchema):
        with transaction.atomic():
            certificate_issuance = CertificateIssuance.objects.select_for_update().filter(certification=item).first()
            if item.certificate_issuance_status != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")
            certificate_issuance, _ = CertificateIssuance.objects.update_or_create(
                certification=item,
                defaults={
                    "certificate_file_id": data.certificate_file_id,
                },
            )
            return certificate_issuance

    @staticmethod
    def next_step_or_finish(item: Certification, current_step: CERTIFICATION_STEPS):
        # 下一步操作。如果当前步骤需要文件，文件必须存在；必须填写了内容；如果有选否的，不能执行
        content = None
        need_file = False
        file = None
        has_bool = False
        status_key = ""
        next_key = ""
        b = True
        ok = False

        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.certification_status != CERTIFICATION_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            if current_step == CERTIFICATION_STEPS.PLAN.value:
                status_key = "plan_status"
                content = item.plan
                need_file = True
                file = content.file_id
                next_key = "document_review_status"

            if current_step == CERTIFICATION_STEPS.DOCUMENT_REVIEW.value:
                status_key = "document_review_status"
                content = item.document_review
                has_bool = True
                b = False
                if content is not None:
                    b = content.model_accepted and content.material_accepted
                next_key = [
                    "onsite_inspection_status",
                    "carbon_footprint_verification_status",
                ]

            if current_step == CERTIFICATION_STEPS.ONSITE_INSPECTION.value:
                status_key = "onsite_inspection_status"
                content = item.onsite_inspection
                need_file = True
                has_bool = True
                if content is not None:
                    file = content.file_id
                    b = (
                        content.capability_accepted
                        and content.name_file_accepted
                        and content.design_file_accepted
                        and content.design_product_accepted
                    )
                if item.carbon_footprint_verification_status == CERTIFICATION_STEP_STATUS.ONGOING.value:
                    next_key = "carbon_footprint_verification_status"
                else:
                    next_key = "verification_report_issuance_status"

            if current_step == CERTIFICATION_STEPS.CARBON_FOOTPRINT_VERIFICATION.value:
                status_key = "carbon_footprint_verification_status"
                content = item.carbon_footprint_verification
                has_bool = True
                if content is not None:
                    b = content.result
                if item.onsite_inspection_status == CERTIFICATION_STEP_STATUS.ONGOING.value:
                    next_key = "onsite_inspection_status"
                else:
                    next_key = "verification_report_issuance_status"

            if current_step == CERTIFICATION_STEPS.VERIFICATION_REPORT_ISSUANCE.value:
                status_key = "verification_report_issuance_status"
                content = item.verification_report_issuance
                next_key = "review_decision_status"

            if current_step == CERTIFICATION_STEPS.REVIEW_DECISION.value:
                status_key = "review_decision_status"
                content = item.review_decision
                has_bool = True
                if content is not None:
                    b = content.result
                next_key = "certificate_issuance_status"

            if current_step == CERTIFICATION_STEPS.CERTIFICATE_ISSUANCE.value:
                status_key = "certificate_issuance_status"
                content = item.certificate_issuance
                next_key = []  # 没有下一步了
                ok = True

            if getattr(item, status_key) != CERTIFICATION_STEP_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")
            if content is None:
                raise HttpError(422, "当前状态不允许该操作(请提交相关内容)")
            if need_file and file is None:
                raise HttpError(422, "当前状态不允许该操作(请上传相关文件)")

            setattr(item, status_key, CERTIFICATION_STEP_STATUS.COMPLETED.value)
            if not isinstance(next_key, list):
                next_key = [next_key]
            if ok:
                # 完成认证
                item.certification_status = CERTIFICATION_STATUS.APPROVED.value
                item.certification_finish_time = datetime.now()
            if has_bool and b is False:
                # 拒绝认证
                setattr(item, status_key, CERTIFICATION_STEP_STATUS.REJECTED.value)
                item.certification_status = CERTIFICATION_STATUS.REJECTED.value
                item.certification_finish_time = datetime.now()
                # 如果是现场检查/碳足迹核查某个被拒绝，则另一个设置为未开始
                if current_step == CERTIFICATION_STEPS.ONSITE_INSPECTION.value:
                    if item.carbon_footprint_verification_status == CERTIFICATION_STEP_STATUS.ONGOING.value:
                        item.carbon_footprint_verification_status = None
                if current_step == CERTIFICATION_STEPS.CARBON_FOOTPRINT_VERIFICATION.value:
                    if item.onsite_inspection_status == CERTIFICATION_STEP_STATUS.ONGOING.value:
                        item.onsite_inspection_status = None
            else:
                # 设置下一步
                for key in next_key:
                    setattr(item, key, CERTIFICATION_STEP_STATUS.ONGOING.value)

            item.save()
        return item

    @staticmethod
    def get_authority_index(authority: Authority):
        # 下月1号
        return dict(
            application_num=Certification.objects.filter(
                authority=authority,
                application_status=CERTIFICATION_APPLICATION_STATUS.WAITING.value,
            ).count(),  # 待处理处理个数提示
            certification_num=Certification.objects.filter(
                authority=authority,
                certification_status__in=[
                    CERTIFICATION_STATUS.WAITING.value,
                    CERTIFICATION_STATUS.ONGOING.value,
                ],
            ).count(),  # 待认证、认证中个数 提示
            supervision_num=Supervision.objects.filter(
                certification__authority=authority,
                status=SUPERVISION_STATUS.ONGOING.value,
            ).count(),  # 监督中个数提示
            this_month_application_num=Certification.objects.filter(
                authority=authority, create_time__gte=datetime.now().replace(day=1)
            ).count(),  # 本月申请
            ongoing_certification_num=Certification.objects.filter(
                authority=authority,
                certification_status=CERTIFICATION_STATUS.ONGOING.value,
            ).count(),  # 认证中
            issued_certificate_num=Certification.objects.filter(
                authority=authority,
                certification_status=CERTIFICATION_STATUS.APPROVED.value,
            ).count(),  # 发证数量
            total_application_num=Certification.objects.filter(authority=authority).count(),  # 累计申请
        )

    @staticmethod
    def start_supervision(item: Certification) -> Supervision:
        with transaction.atomic():
            item = Certification.objects.select_for_update().get(pk=item.id)
            if item.certification_status != CERTIFICATION_STATUS.APPROVED.value:
                raise HttpError(422, "当前状态不允许该操作(认证未通过)")
            if item.supervision_status == SUPERVISION_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作(有正在进行中的监督)")
            supervision = Supervision.objects.create(
                certification=item,
                authority=item.authority,
                manufacturer=item.manufacturer,
                status=SUPERVISION_STATUS.ONGOING.value,
                start_time=datetime.now(),
            )
            item.supervision_status = SUPERVISION_STATUS.ONGOING.value
            item.last_supervision = supervision
            item.save()
        return supervision

    @staticmethod
    def check_supervision(item: Supervision, data: SupervisionInSchema, draft: bool = False) -> Supervision:
        with transaction.atomic():
            item = Supervision.objects.select_for_update().get(pk=item.id)
            if item.status != SUPERVISION_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")

            for key, value in data.model_dump().items():
                setattr(item, key, value)
            item.save()
        return item

    @staticmethod
    def finish_supervision(item: Supervision) -> Supervision:
        with transaction.atomic():
            item = Supervision.objects.select_for_update().get(pk=item.id)
            if item.status != SUPERVISION_STATUS.ONGOING.value:
                raise HttpError(422, "当前状态不允许该操作")
            item.status = SUPERVISION_STATUS.COMPLETED.value
            item.end_time = datetime.now()
            item.save()

            item.certification.supervision_status = SUPERVISION_STATUS.COMPLETED.value
            item.certification.save()
        return item
